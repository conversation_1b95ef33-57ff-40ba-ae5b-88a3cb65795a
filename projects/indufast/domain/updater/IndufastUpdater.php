<?php

  namespace domain\updater;

  use Gsd\Updater\ExecutePrivilegeUpdate;
  use Gsd\Updater\GsdUpdater;
  use PrivilegeDefault;

  class IndufastUpdater extends GsdUpdater {
    protected function execute37() {
      $this->executeQuery("ALTER TABLE `indufast_google_drive_file` DROP FOREIGN KEY `fk_project_id`;");
      $this->executeQuery("ALTER TABLE `indufast_google_drive_file` DROP COLUMN `project_id`;");
      $this->executeQuery("ALTER TABLE `indufast_google_drive_file` ADD COLUMN `calendar_event_id` MEDIUMINT(8) UNSIGNED NULL AFTER `url`;");
      $this->executeQuery("ALTER TABLE `indufast_google_drive_file` ADD CONSTRAINT `fk_calendar_event_id` FOREIGN KEY (`calendar_event_id`) REFERENCES `indufast_calendar_event`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;");
      return true;
    }

    protected function execute36() {
      $this->executeQuery("ALTER TABLE `indufast_workday_special_hours` CHANGE `type` `type` ENUM('leave', 'special-leave', 'sick', 'unexcused-leave') NOT NULL;");
      return true;
    }

    protected function execute35() {
      $this->executeQuery("DELETE FROM indufast_workday WHERE date < '2025-08-01';");
      return true;
    }

    protected function execute34() {
      $this->executeQuery("
        CREATE TABLE IF NOT EXISTS indufast_workday_special_hours (
            id         mediumint    unsigned AUTO_INCREMENT    PRIMARY KEY,
            workday_id mediumint    unsigned                   NOT NULL,
            duration   time                                    NOT NULL,
            type       enum ('leave', 'special-leave', 'sick') NOT NULL,
            CONSTRAINT fk_workday_id FOREIGN KEY (workday_id) REFERENCES indufast_workday (id) ON UPDATE CASCADE ON DELETE CASCADE
        );
      ");
      return true;
    }

    protected function execute32() {
      $indufastOrganisationId = INDUFAST_ORGANISATION_ID;
      $this->executeQuery("UPDATE `user` SET `organisation_id` = $indufastOrganisationId WHERE `organisation_id` IS NULL");
      return true;
    }
    protected  function execute31(): bool {
      $this->executeQuery("ALTER TABLE `indufast_workday` ADD COLUMN IF NOT EXISTS `break_time` TIME NULL AFTER `employee_id`;");
      $this->executeQuery("ALTER TABLE `indufast_workday` ADD COLUMN IF NOT EXISTS `work_time` TIME NULL AFTER `break_time`;");
      return true;
    }

    protected function execute30(): bool {
      $this->executeQuery("
        ALTER TABLE `indufast_employee` 
            ADD IF NOT EXISTS `non_working_days_even_weeks` TEXT NULL AFTER `monthly_percentage`, 
            ADD IF NOT EXISTS `non_working_days_uneven_weeks` TEXT NULL AFTER `non_working_days_even_weeks`;
      ");
      return true;
    }

    protected function execute29(): bool {
      $this->executeQuery("ALTER TABLE `indufast_calendar_event` ADD COLUMN IF NOT EXISTS `confirmed` TINYINT(1) NULL AFTER `end`;");
      $this->executeQuery("ALTER TABLE `indufast_calendar_event` ADD COLUMN IF NOT EXISTS `remark` TEXT NULL AFTER `confirmed`;");
      return true;
    }

    protected function execute28(): bool {
      $this->executeQuery("
        CREATE TABLE IF NOT EXISTS indufast_workday_summary (
            id              mediumint    unsigned AUTO_INCREMENT PRIMARY KEY,
            year            smallint     unsigned                NOT NULL,
            month           tinyint      unsigned                NOT NULL,
            employee_id     mediumint(8) unsigned                NOT NULL,
            monthly_balance time                                 NOT NULL,
            CONSTRAINT  fk_employee_id FOREIGN KEY (employee_id) REFERENCES indufast_employee (id) ON UPDATE CASCADE ON DELETE CASCADE
        );
      ");
      $this->executeQuery("ALTER TABLE `indufast_employee` ADD COLUMN IF NOT EXISTS `monthly_percentage` INT UNSIGNED NOT NULL DEFAULT 100 AFTER `active`;");
      return true;
    }
    protected function execute27(): bool {
      $this->executeQuery("ALTER TABLE `indufast_employee` ADD COLUMN IF NOT EXISTS `usergroup` VARCHAR(20) NULL AFTER `active`;");
      $this->executeQuery("UPDATE `indufast_employee` SET `usergroup` = 'ADMIN' WHERE `may_login` = 1 AND `usergroup` IS NULL;");

      PrivilegeDefault::destroyByCode("M_VIEWER");
      PrivilegeDefault::addOther("M_VIEWER", "Access get requests", [\User::USERGROUP_ADMIN, \User::USERGROUP_SUPERADMIN, \User::USERGROUP_VIEWER]);

      PrivilegeDefault::destroyByCode("M_CHANGELOG");
      PrivilegeDefault::addOther("M_CHANGELOG", "Access the changelog", [\User::USERGROUP_ADMIN, \User::USERGROUP_SUPERADMIN, \User::USERGROUP_VIEWER]);
      return true;
    }
    protected function execute26(): bool {
      $this->executeQuery("ALTER TABLE `indufast_employee` MODIFY COLUMN `rank` VARCHAR(1) NULL;");
      $this->executeQuery("ALTER TABLE `indufast_employee` MODIFY COLUMN `rank_number` INT NULL;");
      return true;
    }
    protected function execute25(): bool {
      $this->executeQuery("
        CREATE TABLE IF NOT EXISTS indufast_google_drive_file (
            id         mediumint unsigned AUTO_INCREMENT PRIMARY KEY,
            name       varchar(255) NOT NULL,
            url        varchar(255) NOT NULL,
            icon_url   varchar(255) NULL,
            project_id mediumint unsigned NOT NULL,
            insertTS   datetime NOT NULL,
            updateTS   datetime NULL,
            CONSTRAINT fk_project_id FOREIGN KEY (project_id) REFERENCES indufast_project (id) ON UPDATE CASCADE ON DELETE CASCADE
        );
      ");
      return  true;
    }
    protected function execute24():bool {
      $this->executeQuery("ALTER TABLE `indufast_employee` ADD COLUMN IF NOT EXISTS `may_login` TINYINT(1) NOT NULL DEFAULT 0 AFTER `active`;");
      return true;
    }

    protected function execute23():bool {
      $this->executeQuery("ALTER TABLE `indufast_employee` ADD COLUMN IF NOT EXISTS `name_accredis` VARCHAR(255) NULL AFTER `name`;");
      $this->executeQuery("ALTER TABLE `indufast_workday` DROP FOREIGN KEY IF EXISTS `user_id`;");
      $this->executeQuery("ALTER TABLE `indufast_workday` DROP COLUMN IF EXISTS `user_id`;");
      $this->executeQuery("ALTER TABLE `indufast_workday` ADD COLUMN IF NOT EXISTS `employee_id` MEDIUMINT(8) UNSIGNED NOT NULL;");
      $this->executeQuery("CREATE INDEX IF NOT EXISTS `idx_employee_id` ON `indufast_workday` (`employee_id`);");
      $this->executeQuery("ALTER TABLE `indufast_workday` DROP FOREIGN KEY IF EXISTS `fk_workday_employee_id`;");
      $this->executeQuery("ALTER TABLE `indufast_workday` ADD CONSTRAINT `fk_workday_employee_id` FOREIGN KEY (`employee_id`) REFERENCES `indufast_employee`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;");
      $this->executeQuery("ALTER TABLE `indufast_workday_line` MODIFY COLUMN `distance` DECIMAL(10,1) NULL;");
      return true;
    }

    protected function execute22(): bool {
      $this->executeQuery("ALTER TABLE `indufast_calendar_event_employee` CHANGE `has_conflicts` `conflict` JSON NULL;");
      $this->executeQuery("UPDATE `indufast_calendar_event_employee` SET `conflict` = NULL WHERE `conflict` IS NOT NULL;");
      return true;
    }

    protected function execute21():bool {
      $this->executeQuery("ALTER TABLE `indufast_project` CHANGE `status` `status` ENUM('planning','incomplete','weather_dependent','complete','unplanned') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL;");
      return true;
    }

    protected function execute20(): bool {
      $this->executeQuery("ALTER TABLE `indufast_calendar_event` CHANGE `status` `type` ENUM('work', 'blast') NOT NULL DEFAULT 'work';");
      return true;
    }

    protected function execute19():bool {
      $this->executeQuery("ALTER TABLE `indufast_calendar_event` ADD `status` ENUM('work', 'blast') NOT NULL DEFAULT 'work';");
      return true;
    }

    protected function execute18():bool {
      $this->executeQuery("ALTER TABLE `indufast_calendar_event` ADD `fte` INT UNSIGNED NULL;");
      return true;
    }
    protected function execute17():bool {
      $this->executeQuery("ALTER TABLE `indufast_project` CHANGE `status` `status` ENUM('blast_sand','planning','incomplete','weather_dependent','complete','unplanned') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL;");
      return true;
    }

    protected function execute16():bool {
      $this->executeQuery("ALTER TABLE `indufast_project` ADD `fte` INT UNSIGNED NULL AFTER `remark`");
      return true;
    }

    protected function execute15(): bool {
      $this->executeQuery("ALTER TABLE `indufast_project` DROP FOREIGN KEY material_load_employee_id");
      $this->executeQuery("ALTER TABLE `indufast_project` DROP FOREIGN KEY team_lead_employee_id");
      $this->executeQuery("ALTER TABLE `indufast_project` DROP `material_load_employee_id`");
      $this->executeQuery("ALTER TABLE `indufast_project` DROP `team_lead_employee_id`");
      $this->executeQuery("
        ALTER TABLE `indufast_calendar_event`
            ADD `material_load_employee_ids` JSON NULL,
            ADD `team_lead_employee_id` MEDIUMINT(8) UNSIGNED NULL;
      ");
      $this->executeQuery("
        ALTER TABLE `indufast_calendar_event`
            ADD CONSTRAINT `team_lead_employee_id` FOREIGN KEY (`team_lead_employee_id`) REFERENCES `indufast_employee`(`id`) ON DELETE SET NULL;
      ");
      return true;
    }

    protected function execute14(): bool {
      $this->executeQuery("ALTER TABLE `indufast_calendar_event_employee` ADD `has_conflicts` TINYINT(1) NOT NULL DEFAULT 0;");
      return true;
    }

    protected function execute13(): bool {
      PrivilegeDefault::addOther("M_CHANGELOG", "Access the changelog", [\User::USERGROUP_ADMIN, \User::USERGROUP_SUPERADMIN]);
      return true;
    }

    protected function execute12(): bool {
      $this->executeQuery("ALTER TABLE `indufast_project` DROP FOREIGN KEY project_leader_user_id");
      $this->executeQuery("ALTER TABLE `indufast_project` DROP `project_leader_user_id`");
      $this->executeQuery("
        ALTER TABLE `indufast_project` 
            ADD `material_load` ENUM('bladel','alblasserdam') NULL AFTER `address`, 
            ADD `material_load_employee_id` MEDIUMINT(8) UNSIGNED NULL AFTER `material_load`, 
            ADD `team_lead_employee_id` MEDIUMINT(8) UNSIGNED NULL AFTER `material_load_employee_id`;
      ");
      $this->executeQuery("
        ALTER TABLE `indufast_project` 
            ADD CONSTRAINT `material_load_employee_id` FOREIGN KEY (`material_load_employee_id`) REFERENCES `indufast_employee`(`id`) ON DELETE CASCADE ON UPDATE CASCADE; 
      ");
      $this->executeQuery("
        ALTER TABLE `indufast_project` 
            ADD CONSTRAINT `team_lead_employee_id` FOREIGN KEY (`team_lead_employee_id`) REFERENCES `indufast_employee`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
      ");
      return true;
    }

    protected function execute11() {
      $this->executeQuery("
        ALTER TABLE `indufast_project` 
            ADD `status` 
                ENUM('blast_sand','planning','incomplete','weather_dependent','complete') 
                NOT NULL 
                AFTER `name`;
      ");
      return true;
    }

    protected function execute10(): bool {
      $this->executeQuery("create table indufast_calendar_event_employee
        (
            id                mediumint unsigned auto_increment primary key,
            calendar_event_id mediumint unsigned not null,
            employee_id       mediumint unsigned not null,
            constraint calendar_event_id
                foreign key (calendar_event_id) references indufast_calendar_event (id)
                    on update cascade on delete cascade,
            constraint employee_id
                foreign key (employee_id) references indufast_employee (id)
                    on update cascade on delete cascade
        );
      ");
      return true;
    }

    protected function execute9(): bool {
      PrivilegeDefault::addOther("M_ADMINISTER_EMPLOYEES", "Administer projects", [\User::USERGROUP_ADMIN, \User::USERGROUP_SUPERADMIN]);
      return true;
    }

    protected function execute8(): bool {
      $this->executeQuery("create table indufast_employee
        (
            id          mediumint unsigned auto_increment
                primary key,
            name        varchar(255)                    not null,
            type        enum ('staff', 'subcontractor') not null,
            email       varchar(255)                    not null,
            extra_email varchar(255)                    null,
            `rank`      varchar(1)                      not null,
            rank_number int                             not null,
            industries  text                            null,
            private     tinyint(1)                      not null,
            team_lead   tinyint(1)                      not null,
            active      tinyint(1)                      not null,
            insertTS    datetime                        not null,
            updateTS    datetime                        null
        );
      ");

      return true;
    }

    protected function execute7() {
      $this->executeQuery("ALTER TABLE `indufast_project`
        DROP `google_calendar_id`,
        DROP `start`,
        DROP `end`;
      ");

      $this->executeQuery("create table indufast_calendar_event (
        id                       mediumint unsigned auto_increment
            primary key,
        project_id               mediumint unsigned not null,
        google_calendar_event_id varchar(32)        not null,
        start                    datetime           not null,
        end                      datetime           not null,
        constraint project_id
            foreign key (project_id) references indufast_project (id)
                on update cascade on delete cascade
      );
    ");

      return true;
    }

    /**
     * Add the M_ADMINISTER_PROJECTS privilege.
     *
     * @return true
     * @throws \GsdException
     */
    protected function execute6(): bool {
      PrivilegeDefault::addOther("M_ADMINISTER_PROJECTS", "Administer projects", [\User::USERGROUP_ADMIN, \User::USERGROUP_SUPERADMIN]);
      return true;
    }

    /**
     * Add the induast_project table.
     */
    protected function execute5() {
      $this->executeQuery("
      create table indufast_project
        (
            id                     mediumint unsigned auto_increment
                primary key,
            name                   varchar(255)         not null,
            remark                 text                 null,
            google_calendar_id     varchar(32)          null,
            project_number         varchar(32)          not null,
            project_number_exact   varchar(32)          not null,
            project_leader_user_id mediumint unsigned   null,
            start                  datetime             null,
            end                    datetime             null,
            customer_name          varchar(255)         not null,
            contact_name           varchar(255)         null,
            contact_email          varchar(255)         null,
            contact_number         varchar(32)          null,
            address                varchar(255)         not null,
            void                   tinyint(1) default 0 not null,
            insertTS               datetime             not null,
            updateTS               datetime             null,
            constraint project_leader_user_id
                foreign key (project_leader_user_id) references user (id)
                    on update cascade on delete cascade
        )"
      );

      return true;
    }

    /**
     * Sync Accredis database changes.
     */
    protected function execute4() {
      $this->executeQuery("alter table indufast_workday add travel_to_end_line_id mediumint unsigned null;");
      $this->executeQuery("alter table indufast_workday add constraint travel_to_end_line_id foreign key (travel_to_end_line_id) references indufast_workday_line (id) on update cascade on delete cascade;");
      $this->executeQuery("alter table indufast_workday add travel_from_start_line_id mediumint unsigned null;");
      $this->executeQuery("alter table indufast_workday add constraint travel_from_start_line_id foreign key (travel_from_start_line_id) references indufast_workday_line (id) on update cascade on delete cascade;");
      $this->executeQuery("alter table indufast_workday add status enum ('new', 'processed', 'synced') default 'new' not null;");

      $this->executeQuery("ALTER TABLE `indufast_workday_line` CHANGE `start_address` `start_address` VARCHAR(256) NULL;");
      $this->executeQuery("ALTER TABLE `indufast_workday_line` CHANGE `end_address` `end_address` VARCHAR(256) NULL;");
      $this->executeQuery("ALTER TABLE `indufast_workday_line` CHANGE `distance` `distance` DECIMAL(10,1) NULL;");
      $this->executeQuery("ALTER TABLE `indufast_workday_line` CHANGE `vehicle` `vehicle` VARCHAR(256) NULL;");

      return true;
    }

    /**
     * Use the privilege tables and add the M_ACCREDIS privilege.
     */
    protected function execute3() {
      $executor = new ExecutePrivilegeUpdate($this);
      $result = $executor->run();
      PrivilegeDefault::addNewPage("M_ACCREDIS", "Accredis", [\User::USERGROUP_ADMIN, \User::USERGROUP_SUPERADMIN]);
      return $result;
    }

    /**
     * Add the indufast_workday_line table.
     */
    protected function execute2(): bool {
      $this->executeQuery("
        CREATE TABLE IF NOT EXISTS indufast_workday_line (
            id            mediumint unsigned AUTO_INCREMENT PRIMARY KEY,
            workday_id    mediumint unsigned                                                           NOT NULL,
            external_id   mediumint(8)                                                                 NULL,
            start         time                                                                         NOT NULL,
            end           time                                                                         NOT NULL,
            start_address varchar(256)                                                                 NOT NULL,
            end_address   varchar(256)                                                                 NOT NULL,
            distance      decimal(10, 1)                                                               NOT NULL,
            type          enum ('travel-to', 'travel-from', 'hours', 'leave', 'special-leave', 'sick') NULL,
            vehicle       varchar(256)                                                                 NOT NULL,
            void          tinyint(1)                                                                   NOT NULL,
            remark        text                                                                         NULL,
            insertTS      datetime                                                                     NOT NULL,
            updateTS      datetime                                                                     NULL,
            CONSTRAINT workday_id
                FOREIGN KEY (workday_id) REFERENCES indufast_workday (id)
                    ON UPDATE CASCADE ON DELETE CASCADE
        );
      ");

      return true;
    }

    /**
     * Add the indufast_workday table.
     */
    protected function execute1(): bool {
      $this->executeQuery("
        CREATE TABLE IF NOT EXISTS indufast_workday (
          id       mediumint unsigned AUTO_INCREMENT PRIMARY KEY,
          user_id  mediumint unsigned                                NOT NULL,
          date     date                                              NOT NULL,
          type     enum ('travel', 'leave', 'special-leave', 'sick') NULL,
          remark   text                                              NULL,
          insertTS datetime                                          NOT NULL,
          updateTS datetime                                          NULL,
          CONSTRAINT user_id
              FOREIGN KEY (user_id) REFERENCES user (id)
                  ON UPDATE CASCADE ON DELETE CASCADE
        );
      ");

      return true;
    }

    public function __construct() {
      $this->setVersionCode(PROJECT . "-version");
    }

  }
