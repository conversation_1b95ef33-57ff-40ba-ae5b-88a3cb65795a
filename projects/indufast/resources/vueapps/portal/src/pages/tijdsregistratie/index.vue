<script setup>
import {onMounted, ref} from "vue";
import {useTitle} from 'vue-page-title';
import {useRouter} from 'vue-router';
import createApiService from '@/services/api';
import {definePage} from "unplugin-vue-router/runtime";

definePage({
  meta: {
    title: 'Tijdsregistratie - Overzicht',
  },
})

const {title} = useTitle();
const router = useRouter();
const api = createApiService(router);
const currentYear = new Date().getFullYear();
const currentMonth = new Date().getMonth() + 1;

const employees = ref([]);
const loading = ref(false);
const search = ref('');

const employeeHeader = [
  {title: "Naam", value: "name", sortable: true, nowrap: true},
  {title: "Naam in Accredis", value: "name_accredis", sortable: true, nowrap: true},
  {title: "Open dagen", value: "newWorkdayCount", cellProps: {class: "no-wrap"}, sortable: true, nowrap: true},
  {title: "Actief", value: "active", cellProps: {class: "no-wrap"}, sortable: true, nowrap: true},
  {title: "Acties", value: "actions", cellProps: {class: "no-wrap center"}},
];

const fetchEmployees = async () => {
  loading.value = true;
  api
    .get("employeeList?" + new URLSearchParams({
      accredis: true,
    }).toString())
    .then((response) => {
      console.log(response.data);
      loading.value = false;
      employees.value = response.data.data;
    })
    .catch((error) => {
      loading.value = false;
      console.log(error);
    });
};

const openDetails = (event, row) => {
  router.push({
    name: 'time-registration-details',
    params: { employee: row.item.id, year: currentYear, month: currentMonth }
  });
};

onMounted(() => {
  fetchEmployees();
});

</script>

<template>
  <v-card>
    <v-card-title class="space-between pt-4 pb-4">
      <span>
        {{ title }}
        <v-icon
          icon="mdi-refresh"
          size="small"
          color="primary"
          @click="fetchEmployees"
        />
      </span>
    </v-card-title>
    <v-divider />
    <v-card-text>
      <v-data-table
        :headers="employeeHeader"
        :items="employees"
        item-key="id"
        density="compact"
        :loading="loading"
        must-sort
        :sort-by="[{key: 'name', order: 'asc'}]"
        :search="search"
        class="stickyFirstColumn stickyLastColumn rowHover"
        @click:row="openDetails"
      >
        <template #item.active="{ item }">
          <v-icon :color="item.active ? 'indufastGreen' : 'indufastRed'">
            {{ item.active ? 'mdi-check' : 'mdi-close' }}
          </v-icon>
        </template>
        <template #item.actions="{ item }">
          <router-link
            :to="{ name: 'time-registration-details', params: { employee: item.id, year: currentYear, month: currentMonth } }"
          >
            <v-icon
              color="primary"
              icon="mdi-eye"
            />
          </router-link>
        </template>
      </v-data-table>
    </v-card-text>
  </v-card>
</template>
