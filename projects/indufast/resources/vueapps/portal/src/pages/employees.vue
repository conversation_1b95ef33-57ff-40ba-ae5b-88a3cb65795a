<script setup>
import {onMounted, ref, watch, computed} from "vue";
import {useRouter} from 'vue-router';
import createApiService from '@/services/api';
import {definePage} from "unplugin-vue-router/runtime";
import {employeeTypes, industryTypes, nonWorkingDays, userGroups} from "@/helpers/constants.js";
import {useTitle} from "vue-page-title";
import {translateErrors} from "@/helpers/translateErrors.js";
import {useSnackbarStore} from '@/stores/snackbar';
import {sortNullLast} from "@/helpers/helpers.js";
import SearchBox from "@/components/SearchBox.vue";
import { useAuthenticationStore } from "@/stores/authentication.js";

const router = useRouter();
const api = createApiService(router);
const authStore = useAuthenticationStore();

let employees = ref([]);
let loading = ref(false);
let editEmployeeDialog = ref(false);
let editEmployeeDialogBusy = ref(false);
let editEmployeeData = ref({});
let errors = ref({});
let search = ref('');
const {title} = useTitle();

const snackbarStore = useSnackbarStore();
definePage({
  meta: {
    title: 'Medewerkers',
  },
})

onMounted(() => {
  updateEmployees()
});

const sortType = (a, b) => {
  return employeeTypes.find(type => type.value === a)?.title.localeCompare(employeeTypes.find(type => type.value === b)?.title);
}

const sortRank = (a, b) => {
  const aRank = a.rank ?? 'Z';
  const bRank = b.rank ?? 'Z';
  return aRank !== bRank ? aRank.localeCompare(bRank) : a.rank_number - b.rank_number;
}

const employeeHeader = [
  {title: "Naam", value: "name", sortable: true, nowrap: true, maxWidth: 350},
  {title: "Type", value: "type", cellProps: {class: "no-wrap"}, sort: sortType},
  {title: "Gebruikersgroep", value: "usergroup", cellProps: {class: "no-wrap"}},
  {title: "E-mail", value: "email", sortable: true},
  {title: "Extra e-mail", value: "extra_email", sort: sortNullLast},
  {title: "Rang", value: "rank", cellProps: {class: "no-wrap"}, sortable: true, sortRaw: sortRank},
  {title: "Branches", value: "industries", cellProps: {class: "no-wrap"}},
  {title: "Actief", value: "active", cellProps: {class: "no-wrap center"}, sortable: true},
  {title: "Mag inloggen", value: "may_login", cellProps: {class: "no-wrap center"}, sortable: true},
  {title: "Contactpersoon bouwplaats", value: "team_lead", cellProps: {class: "no-wrap center"}, sortable: true},
  {title: "Privé", value: "private", cellProps: {class: "no-wrap center"}, sortable: true},
  {title: "Accredis", value: "name_accredis", cellProps: {class: "no-wrap center"}, sortable: true},
  {title: "Acties", value: "actions", cellProps: {class: "no-wrap center"}},
];

const editEmployee = (employee) => {
  editEmployeeDialog.value = true;
  editEmployeeData.value = {...employee};
  if (!editEmployeeData.value.non_working_days_even_weeks) {
    editEmployeeData.value.non_working_days_even_weeks = [];
  }
  if (!editEmployeeData.value.non_working_days_uneven_weeks) {
    editEmployeeData.value.non_working_days_uneven_weeks = [];
  }
}

const newEmployee = () => {
  editEmployeeDialog.value = true;
  editEmployeeData.value = {
    active: true,
    may_login: false,
    team_lead: false,
    private: false,
    monthly_percentage: 100,
    non_working_days_even_weeks: [],
    non_working_days_uneven_weeks: [],
  }
}

const submitEditEmployee = () => {
  editEmployeeDialogBusy.value = true;

  let uri = (editEmployeeData.value.id) ? "employeeUpdate?id=" + editEmployeeData.value.id : "employeeCreate";
  api
    .post(uri, JSON.stringify(editEmployeeData.value))
    .then((response) => {
      editEmployeeData.value.id = response.data.data.id;
      editEmployeeDialog.value = false;
      editEmployeeDialogBusy.value = false;
      snackbarStore.showMessage("Medewerker succesvol opgeslagen!", "success");
      updateEmployees();
    })
    .catch((error) => {
      editEmployeeDialogBusy.value = false;
      errors.value = error.response.data.data
      snackbarStore.showMessage("Er is een fout opgetreden bij het opslaan!", "error");
      console.log(error);
    });
}

const updateEmployees = () => {
  loading.value = true;
  api
    .get("employeeList")
    .then((response) => {
      loading.value = false;
      employees.value = response.data.data;
    })
    .catch((error) => {
      loading.value = false;
      errors.value = error.response.data.data
      console.log(error);
    });
}

watch(editEmployeeDialog, (val) => {
  if (!val) {
    errors.value = {};
  }
})

watch(() => editEmployeeData.value.rank, (newRank) => {
  if (!newRank) {
    editEmployeeData.value.rank_number = null;
  }
})

const removeIndustry = (item) => {
  editEmployeeData.industries = editEmployeeData.industries.filter(find => find !== item);
}

const readonly = computed(() => !authStore.hasPermission('edit'));
</script>

<template>
  <v-card>
    <v-card-title class="space-between pt-4 pb-4">
      <span>
        {{ title }}
        <v-icon
          icon="mdi-refresh"
          size="small"
          color="primary"
          @click="updateEmployees"
        />
      </span>
      <div class="d-flex align-center ga-4">
        <search-box v-model="search" />
        <v-btn
          v-if="!readonly"
          color="primary"
          prepend-icon="mdi-plus"
          @click="newEmployee"
        >
          Nieuwe medewerker
        </v-btn>
      </div>
    </v-card-title>
    <v-divider />
    <v-card-text>
      <v-data-table
        :headers="employeeHeader"
        :items="employees"
        item-key="id"
        density="compact"
        :loading="loading"
        must-sort
        :sort-by="[{key: 'name', order: 'asc'}]"
        :search="search"
        class="stickyFirstColumn stickyLastColumn rowHover"
      >
        <template #item.type="{ item }">
          {{ employeeTypes.find(type => type.value === item.type).title }}
        </template>
        <template #item.rank="{ item }">
          <span v-if="item.rank">{{ item.rank }}{{ item.rank_number || '' }}</span>
        </template>
        <template #item.industries="{ item }">
          <v-icon
            v-for="(industry, index) in item.industries"
            :key="index"
            class="mr-1"
            :color="industryTypes.find(type => type.value === industry)?.color"
            :title="industryTypes.find(type => type.value === industry)?.title"
          >
            {{ industryTypes.find(type => type.value === industry)?.icon }}
          </v-icon>
        </template>
        <template #item.private="{ item }">
          <v-icon :color="item.private ? 'indufastGreen' : 'indufastRed'">
            {{ item.private ? 'mdi-check' : 'mdi-close' }}
          </v-icon>
        </template>
        <template #item.team_lead="{ item }">
          <v-icon :color="item.team_lead ? 'indufastGreen' : 'indufastRed'">
            {{ item.team_lead ? 'mdi-check' : 'mdi-close' }}
          </v-icon>
        </template>
        <template #item.active="{ item }">
          <v-icon :color="item.active ? 'indufastGreen' : 'indufastRed'">
            {{ item.active ? 'mdi-check' : 'mdi-close' }}
          </v-icon>
        </template>
        <template #item.may_login="{ item }">
          <v-icon :color="item.may_login ? 'indufastGreen' : 'indufastRed'">
            {{ item.may_login ? 'mdi-check' : 'mdi-close' }}
          </v-icon>
        </template>
        <template #item.name_accredis="{ item }">
          <v-icon :color="item.name_accredis?.length ? 'indufastGreen' : 'indufastRed'">
            {{ item.name_accredis?.length ? 'mdi-check' : 'mdi-close' }}
          </v-icon>
        </template>
        <template #item.usergroup="{ item }">
          {{ userGroups.find(group => group.value === item.usergroup)?.title }}
        </template>
        <template #item.actions="{ item }">
          <v-icon @click="editEmployee(item)" color="primary">
            {{ !readonly ? 'mdi-pencil' : 'mdi-eye' }}
          </v-icon>
        </template>
      </v-data-table>
      <v-dialog
        v-model="editEmployeeDialog"
        width="1000px"
        scrollable
      >
        <v-card>
          <v-card-title class="pa-0">
            <v-toolbar color="primary">
              <v-toolbar-title>
                {{ editEmployeeData.id ? 'Medewerker ' + editEmployeeData.name + ' bewerken' : 'Nieuwe medewerker' }}
              </v-toolbar-title>
              <v-toolbar-items>
                <v-btn
                  icon
                  @click="editEmployeeDialog = false"
                >
                  <v-icon icon="mdi-close" />
                </v-btn>
              </v-toolbar-items>
            </v-toolbar>
          </v-card-title>
          <v-card-text class="pa-2">
            <v-row no-gutters>
              <v-col>
                <v-card>
                  <v-card-title>Medewerker</v-card-title>
                  <v-card-text>
                    <v-text-field
                      v-model="editEmployeeData.name"
                      label="Naam"
                      :readonly="readonly"
                      :error-messages="translateErrors(errors.name, 'Naam')"
                      class="required mb-5"
                    />
                    <v-select
                      v-model="editEmployeeData.type"
                      label="Type"
                      :items="employeeTypes"
                      item-title="title"
                      item-value="value"
                      :readonly="readonly"
                      :error-messages="translateErrors(errors.type, 'Type')"
                      class="required mb-5"
                    />
                    <v-text-field
                      v-model="editEmployeeData.email"
                      label="E-mail"
                      :readonly="readonly"
                      :error-messages="translateErrors(errors.email, 'E-mail')"
                      class="required mb-5"
                    />
                    <v-text-field
                      v-model="editEmployeeData.extra_email"
                      label="Extra e-mail"
                      :readonly="readonly"
                      :error-messages="translateErrors(errors.extra_email, 'Extra e-mail')"
                    />
                  </v-card-text>
                </v-card>
                <v-card>
                  <v-card-title>Rang</v-card-title>
                  <v-card-text>
                    <v-select
                      v-model="editEmployeeData.rank"
                      label="Rang"
                      :items="['A', 'B', 'C', 'D', 'E']"
                      :readonly="readonly"
                      :error-messages="translateErrors(errors.rank, 'Rang')"
                      :clearable="!readonly"
                      class="mb-5"
                      :class="{ required: editEmployeeData.active }"
                    />
                    <v-slider
                      v-if="editEmployeeData.rank"
                      v-model="editEmployeeData.rank_number"
                      min="1"
                      max="10"
                      step="1"
                      thumb-label="always"
                      show-ticks="always"
                      tick-size="2"
                      :readonly="readonly"
                      :error-messages="translateErrors(errors.rank_number, 'Rangnummer')"
                    />
                  </v-card-text>
                </v-card>
              </v-col>
              <v-col>
                <v-card>
                  <v-card-title>Werktijden</v-card-title>
                  <v-card-text>
                    <v-slider
                      v-model="editEmployeeData.monthly_percentage"
                      min="10"
                      max="100"
                      step="10"
                      thumb-label="always"
                      show-ticks="always"
                      tick-size="2"
                      :readonly="readonly"
                      :error-messages="translateErrors(errors.monthly_percentage, 'Maandelijkse percentage')"
                    >
                      <template #thumb-label="{ modelValue }">
                        {{ modelValue }}%
                      </template>
                    </v-slider>
                  </v-card-text>
                  <v-card-title>
                    Roostervrije dagen
                  </v-card-title>
                  <v-card-text>
                    <v-row no-gutters>
                      <v-col>
                        <v-card-subtitle class="pl-0">
                          Even weken
                        </v-card-subtitle>
                        <v-checkbox
                          v-for="(label, name) in nonWorkingDays"
                          :key="name"
                          v-model="editEmployeeData.non_working_days_even_weeks"
                          :label="label"
                          :value="name"
                          color="primary"
                          hide-details
                          density="compact"
                          :readonly="readonly"
                        />
                      </v-col>
                      <v-col>
                        <v-card-subtitle class="pl-0">
                          Oneven weken
                        </v-card-subtitle>
                        <v-checkbox
                          v-for="(label, name) in nonWorkingDays"
                          :key="name"
                          v-model="editEmployeeData.non_working_days_uneven_weeks"
                          :label="label"
                          :value="name"
                          color="primary"
                          hide-details
                          density="compact"
                          :readonly="readonly"
                        />
                      </v-col>
                    </v-row>
                  </v-card-text>
                  <v-card-text>
                    <v-text-field
                      v-model="editEmployeeData.name_accredis"
                      label="Naam in Accredis"
                      :error-messages="translateErrors(errors.name_accredis, 'Naam in Accredis')"
                      :readonly="readonly"
                    />
                  </v-card-text>
                </v-card>
              </v-col>
              <v-col>
                <v-card>
                  <v-card-title>Overig</v-card-title>
                  <v-card-text>
                    <v-select
                      v-model="editEmployeeData.industries"
                      label="Branches"
                      :items="industryTypes"
                      item-title="title"
                      item-value="value"
                      multiple
                      :readonly="readonly"
                    >
                      <template #selection="{ item }">
                        <v-chip
                          :color="item.raw.color"
                          variant="flat"
                          :prepend-icon="item.raw.icon"
                        >
                          {{ item.title }}
                        </v-chip>
                      </template>
                    </v-select>
                    <v-switch
                      v-model="editEmployeeData.active"
                      color="indufastGreen"
                      base-color="indufastRed"
                      label="Actief"
                      hide-details
                      :readonly="readonly"
                    />
                    <v-switch
                      v-model="editEmployeeData.may_login"
                      color="indufastGreen"
                      base-color="indufastRed"
                      label="Mag inloggen"
                      hide-details
                      :readonly="readonly"
                    />
                    <v-select
                      v-if="editEmployeeData.may_login"
                      v-model="editEmployeeData.usergroup"
                      label="Gebruikersgroep"
                      :items="userGroups"
                      item-title="title"
                      item-value="value"
                      :readonly="readonly"
                      :error-messages="translateErrors(errors.usergroup, 'Gebruikersgroep')"
                    />
                    <v-switch
                      v-model="editEmployeeData.team_lead"
                      color="indufastGreen"
                      base-color="indufastRed"
                      label="Contactpersoon bouwplaats"
                      hide-details
                      :readonly="readonly"
                    />
                    <v-switch
                      v-model="editEmployeeData.private"
                      color="indufastGreen"
                      base-color="indufastRed"
                      label="Privé"
                      hide-details
                      :readonly="readonly"
                    />
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
          </v-card-text>
          <v-card-actions v-if="!readonly">
            <v-btn
              variant="elevated"
              color="primary"
              prepend-icon="mdi-content-save"
              :loading="editEmployeeDialogBusy"
              @click="submitEditEmployee"
            >
              Opslaan
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>
    </v-card-text>
  </v-card>
</template>
