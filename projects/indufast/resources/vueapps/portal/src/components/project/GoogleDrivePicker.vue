<script setup>
import { ref, nextTick } from 'vue';
import { defineOptions } from 'vue';
import {useConfigStore} from "@/stores/config.js";
import {useAuthenticationStore} from "@/stores/authentication.js";

const props = defineProps({
  disabled: {
    type: Boolean,
    default: false
  },
  inline: {
    type: Boolean,
    default: false
  }
});

const showPicker = ref(false);
const showReauthDialog = ref(false);
const emit = defineEmits(['picked']);

const configStore = useConfigStore();
const authStore = useAuthenticationStore();

defineOptions({
  isCustomElement: tag => ['drive-picker', 'drive-picker-docs-view'].includes(tag)
});

function handlePickerClose() {
  showPicker.value = false;
}

const setupPickerEventListeners = () => {
  const element = document.querySelector('drive-picker');
  if (element) {
    element.removeEventListener('picker:picked', handlePickerPicked);
    element.removeEventListener('picker:canceled', handlePickerClose);
    element.removeEventListener('picker-oauth-error', handlePickerClose);
    element.removeEventListener('picker-error', handlePickerClose);
    element.removeEventListener('picker-oauth-response', handlePickerNoToken);

    element.addEventListener('picker:picked', handlePickerPicked);
    element.addEventListener('picker:canceled', handlePickerClose);
    element.addEventListener('picker-oauth-error', handlePickerClose);
    element.addEventListener('picker-error', handlePickerClose);
    element.addEventListener('picker-oauth-response', handlePickerNoToken);
  }
};

const showPickerDialog = async () => {
  showPicker.value = true;
  await nextTick();
  setupPickerEventListeners();
};

const validateTokenWithGoogle = async (token) => {
  try {
    const response = await fetch(`https://www.googleapis.com/oauth2/v1/tokeninfo?access_token=${token}`);
    return response.ok;
  } catch (error) {
    console.warn('Token is locally valid but not with Google:', error);
    return false;
  }
};

const openPicker = async () => {
  if (props.disabled) return;

  // Check if token is valid before opening picker
  if (!authStore.isTokenValid) return showReauthDialog.value = true;

  const isTokenValidWithGoogle = await validateTokenWithGoogle(authStore.validToken);
  if (!isTokenValidWithGoogle) return showReauthDialog.value = true;

  await showPickerDialog();
};

const handleContinueWithoutToken = async () => {
  // Clear the invalid token and let picker handle authentication inline
  authStore.clearToken();
  showReauthDialog.value = false;

  await showPickerDialog();
};

function handlePickerPicked(event) {
  emit('picked', event.detail.docs);
  showPicker.value = false;
}

function handlePickerNoToken(event) {
  // auth screen opens a new picker instance when auth was successful so close the current one
  showPicker.value = false;
  authStore.setToken({
    access_token: event.detail.access_token,
    expires_in: event.detail.expires_in
  });
}
</script>

<template>
  <v-icon
    v-if="props.inline"
    class="cursor-pointer"
    :disabled="props.disabled"
    color="primary"
    size="small"
    title="Bestand selecteren"
    @click="openPicker"
  >
    mdi-google-drive
  </v-icon>
  <v-btn
    v-else
    icon="mdi-google-drive"
    :disabled="props.disabled"
    color="primary"
    size="small"
    title="Bestand selecteren"
    class="mr-2"
    @click="openPicker"
  />

  <drive-picker
    v-if="showPicker && configStore.config"
    :client-id="configStore.config.client_id"
    :app-id="configStore.config.app_id"
    :developer-key="configStore.config.developer_key"
    :oauth-token="authStore.validToken"
    :locale="configStore.config.locale"
    :login-hint="authStore.currentUser.email"
    include-granted-scopes="true"
    multiselect="true"
    scope="https://www.googleapis.com/auth/drive.readonly"
    class="picker-dialog"
  >
    <drive-picker-docs-view
      include-folders="true"
    />
  </drive-picker>

  <v-dialog
    v-model="showReauthDialog"
    max-width="500"
  >
    <v-card>
      <v-toolbar color="indufastRed">
        <v-toolbar-title>
          Google authenticatie vereist
        </v-toolbar-title>
      </v-toolbar>

      <v-card-text>
        Je bent niet ingelogd bij Google of je authenticatie is verlopen. De bestandsselectie zal je vragen om in te loggen bij Google.
      </v-card-text>

      <v-card-actions>
        <v-spacer />
        <v-btn
          text="Annuleren"
          @click="showReauthDialog = false"
        />
        <v-btn
          text="Doorgaan"
          color="primary"
          variant="elevated"
          @click="handleContinueWithoutToken"
        />
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<style>
/* Not scoped, as this will render outside the component */
.picker-dialog {
  z-index: 10000 !important;
}
</style>
