<?php

  use classes\ApiResponse;

  trait IndufastWorkdayApiTrait {

    public function executeWorkdayList(): void {
      $rules = [
        'employee_id' => 'required|integer|exists:indufast_employee,id',
        'year' => 'required|date:Y',
        'month' => 'required|date:m',
      ];
      $data = $this->validateData($_GET, $rules)->getValidatedData();

      $workdays = IndufastWorkday::findAllByMonth($data['employee_id'], $data['year'], $data['month']);

      ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, $workdays);
    }

    public function executeGetHolidays(): void {
      ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, IndufastCalendarEvent::holidays());
    }

    /**
     * @throws Exception
     */
    public function executeWorkdayCalculate(): void {
      if (!$workday = IndufastWorkday::find_by_id($_GET['id'] ?? null)) {
        ApiResponse::sendResponseNotFound('Workday not found');
      }
      $workday->fill($this->data)->validateFillable();

      $rules = [
        'workdayLinesData' => 'array',
        'workdayLinesData.*.void' => 'boolean',
      ];
      $data = $this->validateData($this->data, $rules)->getValidatedData();

      $workdayLinesData = $data['workdayLinesData'] ?? [];
      foreach ($workday->lines() as $workdayLine) {
        $workdayLine->setWorkday($workday);
        $workdayLine->void = ($workdayLinesData[$workdayLine->id]['void'] ?? false);
      }

      ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, $workday);
    }

    public function executeWorkdaySummary(): void {
      $rules = [
        'employee_id' => 'required|integer|exists:indufast_employee,id',
        'year' => 'required|date:Y',
        'month' => 'required|date:m',
      ];
      $data = $this->validateData($_GET, $rules)->getValidatedData();

      $workdays = IndufastWorkday::findAllByMonth($data['employee_id'], $data['year'], $data['month'], true);
      foreach ($workdays as $workday) {
        $workday->calculate();
      }

      $employee = IndufastEmployee::find_by_id($data['employee_id']);
      $totalNet = $this->addTimes(array_filter(array_column($workdays, 'workdayDurationNet')));
      $totalSpecialHours = $this->addTimes(array_filter(array_column($workdays, 'specialHoursDuration')));
      $totalForBalance = $this->addTimes([$totalNet, $totalSpecialHours]);
      $hoursPerMonth = $this->formatHoursAsString(IndufastWorkday::HOURS_PER_MONTH * $employee->monthly_percentage / 100);

      // Set balance to 0 if no workdays are found.
      $monthlyBalance = $workdays ? $this->subtractTimes($totalForBalance, $hoursPerMonth) : '00:00:00';
      IndufastWorkdaySummary::setMonthlyBalance($data['year'], $data['month'], $employee->id, $monthlyBalance);

      $summaries = IndufastWorkdaySummary::find_all_by(['employee_id' => $employee->id]);
      $totalBalance = $this->addTimes(array_filter(array_column($summaries, 'monthly_balance')));

      ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, [
        'count'                    => count($workdays),
        'total'                    => $totalNet,
        'hoursPerMonth'            => $hoursPerMonth,
        'monthlyBalance'           => $monthlyBalance,
        'totalBalance'             => $totalBalance,
        'totalSaturday'            => $this->addTimes(array_filter(array_column($workdays, 'workdayDurationSaturdayNet'))),
        'totalSundayOrHoliday'     => $this->addTimes(array_filter(array_column($workdays, 'workdayDurationSundayOrHolidayNet'))),
        'totalOvertimeBelow'       => $this->addTimes(array_filter(array_column($workdays, 'overtimeBelow'))),
        'totalOvertimeAbove'       => $this->addTimes(array_filter(array_column($workdays, 'overtimeAbove'))),
        'totalOutsideWorkingHours' => $this->addTimes(array_filter(array_column($workdays, 'workdayOutsideWorkingHours'))),
        'totalTravel'              => $this->addTimes(array_filter(array_column($workdays, 'travelDurationNet'))),
        'totalLeave'               => $this->addTimes(array_filter(array_column($workdays, 'specialHoursLeave'))),
        'totalSpecialLeave'        => $this->addTimes(array_filter(array_column($workdays, 'specialHoursSpecialLeave'))),
        'totalSick'                => $this->addTimes(array_filter(array_column($workdays, 'specialHoursSick'))),
        'totalUnexcusedLeave'      => $this->addTimes(array_filter(array_column($workdays, 'specialHoursUnexcusedLeave'))),
      ]);
    }

    public function executeWorkdayCreate(): void {
      $workday = new IndufastWorkday();
      $workday->fill($this->data)->validateFillable();
      $workday->save();
      ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, $workday);
    }

    public function executeWorkdayUpdate(): void {
      if (!$workday = IndufastWorkday::find_by_id($_GET['id'] ?? null)) {
        ApiResponse::sendResponseNotFound('Workday not found');
      }

      $workday->fill($this->data)->validateFillable();

      $rules = [
        'workdayLinesData' => 'array',
        'workdayLinesData.*.void' => 'boolean',
      ];
      $data = $this->validateData($this->data, $rules)->getValidatedData();

      $workdayLinesData = $data['workdayLinesData'] ?? [];
      foreach ($workday->lines() as $workdayLine) {
        if (!empty($workdayLinesData[$workdayLine->id])) {
          $workdayLine->void = ($workdayLinesData[$workdayLine->id]['void'] ?? false) ? 1 : 0;
          $workdayLine->updateTS = date("Y-m-d H:i:s");
          $workdayLine->save();
        }
      }

      // Save and return workday.
      $workday->updateTS = date("Y-m-d H:i:s");
      $workday->save();
      ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, $workday);
    }

    public function executeWorkdaySpecialHoursList(): void {
      $rules = [
        'workday_id' => 'required|integer|exists:indufast_workday,id',
      ];
      $data = $this->validateData($_GET, $rules)->getValidatedData();

      $specialHours = IndufastWorkdaySpecialHours::find_all_by(['workday_id' => $data['workday_id']]);
      ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, $specialHours);
    }

    public function executeWorkdaySpecialHoursCreate(): void {
      $specialHours = new IndufastWorkdaySpecialHours();
      $specialHours->fill($this->data)->validateFillable();
      $specialHours->save();

      // Return the updated workday with special hours
      $workday = IndufastWorkday::find_by_id($specialHours->workday_id);
      ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, $workday);
    }

    public function executeWorkdaySpecialHoursUpdate(): void {
      if (!$specialHours = IndufastWorkdaySpecialHours::find_by_id($_GET['id'] ?? null)) {
        ApiResponse::sendResponseNotFound('Special hours not found');
      }

      $specialHours->fill($this->data)->validateFillable();
      $specialHours->save();

      // Return the updated workday with special hours
      $workday = IndufastWorkday::find_by_id($specialHours->workday_id);
      ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, $workday);
    }

    public function executeWorkdaySpecialHoursDelete(): void {
      if (!$specialHours = IndufastWorkdaySpecialHours::find_by_id($_GET['id'] ?? null)) {
        ApiResponse::sendResponseNotFound('Special hours not found');
      }

      $workdayId = $specialHours->workday_id;
      $specialHours->destroy();

      // Return the updated workday with special hours
      $workday = IndufastWorkday::find_by_id($workdayId);
      ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, $workday);
    }
  }