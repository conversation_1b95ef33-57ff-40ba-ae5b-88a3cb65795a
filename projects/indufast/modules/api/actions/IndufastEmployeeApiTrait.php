<?php

  use classes\ApiResponse;

  trait IndufastEmployeeApiTrait {

    public function executeEmployeeCreate(): void {
      $employee = new IndufastEmployee();
      $employee->fill($this->data)->validateFillable();

      try {
        $employee->save();
        ApiResponse::sendResponseOK('Employee saved', $employee);
      }
      catch (\Exception $e) {
        logToFile(__CLASS__, var_export($e->getMessage(), true));
        ApiResponse::sendResponseError('Unknown error occured while saving.');
      }
    }

    public function executeEmployeeGet(): void {
      if (!$employeeId = $_GET['id'] ?? null) {
        ApiResponse::sendResponseError("No employee id specified");
      }

      if (!$employee = IndufastEmployee::find_by(['id' => $employeeId])) {
        ApiResponse::sendResponseError("Invalid employee id specified");
      }

      ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, $employee);
    }

    public function executeEmployeeList(): void {
      $rules = [
        'active' => 'string|in:true,false',
        'accredis' => 'string|in:true,false',
        'sort' => 'string|in:name,rank',
      ];
      $data = $this->validateData($_GET, $rules)->getValidatedData();

      $filters = [];
      if ($data['active']) {
        $filters[] = ($data['active'] == 'true') ? 'active = 1' : 'active = 0';
      }

      if ($data['accredis']) {
        $filters[] = ($data['accredis'] == 'true') ? 'name_accredis IS NOT NULL' : 'name_accredis IS NULL';
      }

      $sort = match ($data['sort'] ?? null) {
        'rank' => 'ORDER BY rank,rank_number ASC',
        default => 'ORDER BY name ASC',
      };

      $filter = ($filters) ? ' WHERE ' . implode(' AND ', $filters) : '';
      $employees = IndufastEmployee::find_all($filter . ' ' . $sort);

      ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, $employees);
    }

    public function executeEmployeeUpdate(): void {
      if (!$employee = IndufastEmployee::find_by_id($_GET['id'] ?? null)) {
        ApiResponse::sendResponseNotFound('Employee not found');
      }

      $employee->fill($this->data)->validateFillable();

      try {
        $employee->save();
        ApiResponse::sendResponseOK('Employee saved', $employee);
      }
      catch (\Exception $e) {
        logToFile(__CLASS__, var_export($e->getMessage(), true));
        ApiResponse::sendResponseError('Unknown error occured while saving.', (DEVELOPMENT) ? $e->getMessage() : '');
      }
    }

    public function executeEmployeeDelete(): void {
      if (!$employee = IndufastEmployee::find_by_id($_GET['id'] ?? null)) {
        ApiResponse::sendResponseNotFound('Employee not found');
      }

      try {
        $employee->destroy();
        ApiResponse::sendResponseOK('Employee deleted', $employee);
      }
      catch (\Exception $e) {
        logToFile(__CLASS__, var_export($e->getMessage(), true));
        ApiResponse::sendResponseError('Unknown error occured while deleting.');
      }
    }

  }