<?php
class BaseIndufastWorkday extends AppModel {

  const DB_NAME = '';
  const TABLE_NAME = 'indufast_workday';
  const OM_CLASS_NAME = 'IndufastWorkday';
  const columns = ['id', 'employee_id', 'date', 'travel_to_end_line_id', 'travel_from_start_line_id', 'type', 'status', 'remark', 'insertTS', 'updateTS'];
  const field_structure = [
    'id'                          => ['type' => 'mediumint', 'length' => '8', 'null' => false],
    'employee_id'                 => ['type' => 'mediumint', 'length' => '8', 'null' => false],
    'date'                        => ['type' => 'date', 'length' => '', 'null' => false],
    'travel_to_end_line_id'       => ['type' => 'mediumint', 'length' => '8', 'null' => true],
    'travel_from_start_line_id'   => ['type' => 'mediumint', 'length' => '8', 'null' => true],
    'type'                        => ['type' => 'enum', 'length' => '4', 'null' => true, 'enums' => ['travel','leave','special-leave','sick']],
    'status'                      => ['type' => 'enum', 'length' => '3', 'null' => false, 'enums' => ['new','processed','synced']],
    'remark'                      => ['type' => 'text', 'length' => '', 'null' => true],
    'insertTS'                    => ['type' => 'datetime', 'length' => '', 'null' => false],
    'updateTS'                    => ['type' => 'datetime', 'length' => '', 'null' => true],
  ];

  protected static array $primary_key = ['id'];
  protected string $auto_increment = 'id';

  public $id, $employee_id, $date, $travel_to_end_line_id, $travel_from_start_line_id, $type, $status, $remark, $insertTS, $updateTS;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  /**
  * Set default object values
  */
  public function setDefaults() {
    $this->status = 'new';
  }

  /**
   * @param array|null $conditions (required)
   * @param string $raw_sql (optional)
   * @return IndufastWorkday[]
   */
  public static function find_all_like(?array $conditions, string $raw_sql = ''): array {
    return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array|null $conditions (required)
   * @param string $raw_sql (optional)
   * @return IndufastWorkday[]
   */
  public static function find_all_by(?array $conditions, string $raw_sql = ''): array {
    return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $raw_sql (optional)
   * @return IndufastWorkday[]
   */
  public static function find_all(string $raw_sql = ''): array {
    return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array|null $conditions (required)
   * @param string $raw_sql (optional)
   * @return IndufastWorkday|false
   */
  public static function find_by(?array $conditions, string $raw_sql = ''): IndufastWorkday|false {
    return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param int|string|null $id (required)
   * @param string $raw_sql (optional)
   * @return IndufastWorkday|false
   */
  public static function find_by_id($id, string $raw_sql = ''): IndufastWorkday|false {
    return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array|null $conditions (required)
   * @param string $raw_sql (optional)
   * @return int
   */
  public static function count_all_by(?array $conditions, string $raw_sql = '') {
    return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array|null $conditions (required)
   * @param string $raw_sql (optional)
   * @return bool
   */
  public static function delete_by(?array $conditions, string $raw_sql = ''): bool {
    return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

}